name: Deploy to GitHub Pages (Fixed)

on:
  push:
    branches: [main]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "20"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build:gh-pages
        env:
          NODE_ENV: production

      - name: List build files (debug)
        run: |
          echo "=== Build directory contents ==="
          ls -la dist/
          echo "=== Assets directory contents ==="
          ls -la dist/assets/
          echo "=== File count ==="
          find dist -type f | wc -l

      - name: Deploy to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./dist
          force_orphan: true
          enable_jekyll: false
          cname: false
