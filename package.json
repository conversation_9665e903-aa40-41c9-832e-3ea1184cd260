{"name": "x-avater", "version": "0.0.0", "private": true, "type": "module", "engines": {"node": "^20.19.0 || >=22.12.0"}, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "build:gh-pages": "cross-env NODE_ENV=production vite build", "build:gh-pages-check": "cross-env NODE_ENV=production vue-tsc && vite build", "build:analyze": "npm run build && node scripts/analyze-bundle.js", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "perf:test": "npm run build && npm run preview"}, "dependencies": {"@mediapipe/selfie_segmentation": "^0.1.1675465747", "@types/node": "^24.3.0", "pinia": "^3.0.3", "typescript": "^5.9.2", "vue": "^3.5.18", "vue-i18n": "^9.14.5", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.1", "autoprefixer": "^10.4.21", "cross-env": "^10.0.0", "daisyui": "^4.12.24", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "vite": "^7.0.6", "vite-plugin-vue-devtools": "^8.0.0", "vue-tsc": "^3.0.6"}, "homepage": "./"}