@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles */
.upload-area {
  @apply border-2 border-dashed border-gray-300 rounded-lg p-8 text-center transition-colors;
}

.upload-area:hover {
  @apply border-primary bg-primary/5;
}

.upload-area.dragover {
  @apply border-primary bg-primary/10;
}

.canvas-container {
  @apply relative w-full max-w-lg mx-auto;
}

.canvas-layer {
  @apply absolute top-0 left-0 w-full h-full;
}

.loading-spinner {
  @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary;
}

/* 响应式布局增强 */
@media (max-width: 768px) {
  .grid-cols-1.lg\\:grid-cols-2 {
    @apply gap-4;
  }

  .canvas-container {
    @apply max-w-sm;
  }

  .upload-area {
    @apply p-6;
  }
}

/* 步骤指示器样式 */
.step-indicator {
  @apply flex items-center justify-center rounded-full transition-all duration-300;
}

.step-indicator.completed {
  @apply bg-green-500 text-white shadow-lg;
}

.step-indicator.current {
  @apply bg-blue-500 text-white shadow-lg ring-4 ring-blue-200;
}

.step-indicator.pending {
  @apply bg-gray-200 text-gray-500;
}

/* 卡片样式增强 */
.card-enhanced {
  @apply bg-white rounded-xl shadow-lg border border-gray-100 transition-all duration-300;
}

.card-enhanced:hover {
  @apply shadow-xl;
}

/* 按钮样式增强 */
.btn-gradient {
  @apply bg-gradient-to-r from-blue-500 to-purple-600 text-white border-none;
}

.btn-gradient:hover {
  @apply from-blue-600 to-purple-700 shadow-lg;
}

/* 动画效果 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.5s ease-out;
}

@keyframes pulse-soft {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

.pulse-soft {
  animation: pulse-soft 2s infinite;
}

/* 进度条样式 */
.progress-bar {
  @apply w-full bg-gray-200 rounded-full h-2 overflow-hidden;
}

.progress-fill {
  @apply h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-out;
}

/* 工具提示样式 */
.tooltip-custom {
  @apply absolute z-50 px-3 py-2 text-sm text-white bg-gray-900 rounded-lg shadow-lg;
}
